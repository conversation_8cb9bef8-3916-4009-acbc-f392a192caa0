import json
import re
from pathlib import Path

root = Path(__file__).resolve().parents[1]
raw = root / 'aaa' / 'questions_raw.txt'
out = root / 'aaa' / 'questions.json'

text = raw.read_text(encoding='utf-8', errors='ignore')

# 清理方向性控制字符、回车等
text = re.sub(r'[\u200e\u200f\u202a-\u202e]', '', text)
text = text.replace('\r', '')
lines = [l.strip() for l in text.split('\n') if l.strip()]

questions = []
cur = None
# 选项分隔符兼容 . ． 、 ） 等
opt_pattern = re.compile(r'^\s*([ABCD])[\.|．|、|\u3001|\)]\s*(.*)')
# 题号分隔符兼容 . ． 、
qnum_pattern = re.compile(r'^\s*([0-9]{1,3})[\.|．|、]\s*(.*)')

for line in lines:
    # 题号起始
    m = qnum_pattern.match(line)
    if m and (m.group(2) or '').strip():
        # 收尾上一题
        if cur:
            if len(cur.get('options', [])) == 4 and cur.get('correctAnswer'):
                questions.append(cur)
        qtext = m.group(2).strip()
        # 移除题干中嵌入的答案（例如：（ D ））
        ma = re.search(r'[（(]\s*([A-D])\s*[）)]', qtext)
        cur = {'question': re.sub(r'[（(]\s*[A-D]\s*[）)]', '', qtext).strip(), 'options': []}
        if ma:
            cur['correctAnswer'] = ma.group(1)
        continue

    # 选项行（单独一行）
    mo = opt_pattern.match(line)
    if mo and cur is not None:
        label = mo.group(1)
        text = mo.group(2).strip()
        cur['options'].append(f"{label}." + text if not text.startswith(f"{label}.") else text)
        continue

    # 紧凑选项行：A. ...    B. ...    C. ...    D. ...
    if cur and all(k in line for k in ['A', 'B', 'C', 'D']):
        items = re.findall(r'([ABCD])[\.|．|、|\u3001|\)]\s*([^ABCD]+?)(?=(?:\s+[ABCD][\.|．|、|\u3001|\)]|$))', line)
        if items:
            cur['options'] = [f"{k}." + v.strip() for k, v in items]
            continue

    # 独立答案行
    if cur and 'correctAnswer' not in cur:
        ma = re.search(r'^(?:答案|正确答案)\s*[:：]?\s*([A-D])', line)
        if ma:
            cur['correctAnswer'] = ma.group(1)
            continue

# 收尾最后一题
if cur:
    if len(cur.get('options', [])) == 4 and cur.get('correctAnswer'):
        questions.append(cur)

# 过滤无效题
questions = [q for q in questions if len(q.get('options', [])) == 4 and q.get('question')]

out.write_text(json.dumps(questions, ensure_ascii=False, indent=2), encoding='utf-8')
print(f"Parsed {len(questions)} questions -> {out}")

