//
//  QuestionListViewController.swift
//  aaa
//
//  Created by Augment on 2025/8/30.
//

import UIKit

class QuestionListViewController: UIViewController, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    private let total: Int
    private let statuses: [Int: Bool] // true: correct, false: incorrect; missing: unanswered
    private let current: Int
    var onSelect: ((Int) -> Void)?

    private var collectionView: UICollectionView!

    init(total: Int, statuses: [Int: Bool], current: Int) {
        self.total = total
        self.statuses = statuses
        self.current = current
        super.init(nibName: nil, bundle: nil)
        modalPresentationStyle = .pageSheet
    }

    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }

    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .systemBackground
        title = "题目列表"

        let layout = UICollectionViewFlowLayout()
        layout.minimumInteritemSpacing = 12
        layout.minimumLineSpacing = 12
        layout.sectionInset = UIEdgeInsets(top: 16, left: 16, bottom: 16, right: 16)

        collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.register(NumberCell.self, forCellWithReuseIdentifier: "cell")
        view.addSubview(collectionView)

        NSLayoutConstraint.activate([
            collectionView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            collectionView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            collectionView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            collectionView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }

    // MARK: - UICollectionViewDataSource
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int { total }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "cell", for: indexPath) as! NumberCell
        let idx = indexPath.item
        cell.configure(number: idx + 1, status: statuses[idx], isCurrent: idx == current)
        return cell
    }

    // MARK: - UICollectionViewDelegate
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        dismiss(animated: true) { [weak self] in
            self?.onSelect?(indexPath.item)
        }
    }

    // MARK: - UICollectionViewDelegateFlowLayout
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        // 5 列网格
        let inset: CGFloat = 16
        let spacing: CGFloat = 12
        let columns: CGFloat = 5
        let totalSpacing = (columns - 1) * spacing + 2 * inset
        let width = collectionView.bounds.width
        let side = floor((width - totalSpacing) / columns)
        return CGSize(width: side, height: side)
    }
}

private class NumberCell: UICollectionViewCell {
    private let label = UILabel()

    override init(frame: CGRect) {
        super.init(frame: frame)
        contentView.backgroundColor = .white
        contentView.layer.borderWidth = 2
        contentView.layer.borderColor = UIColor.systemGray4.cgColor
        contentView.layer.cornerRadius = frame.size.width / 2
        contentView.layer.masksToBounds = true

        label.translatesAutoresizingMaskIntoConstraints = false
        label.font = UIFont.boldSystemFont(ofSize: 16)
        label.textAlignment = .center
        contentView.addSubview(label)

        NSLayoutConstraint.activate([
            label.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            label.centerYAnchor.constraint(equalTo: contentView.centerYAnchor)
        ])
    }

    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }

    override func layoutSubviews() {
        super.layoutSubviews()
        contentView.layer.cornerRadius = contentView.bounds.width / 2
    }

    func configure(number: Int, status: Bool?, isCurrent: Bool) {
        label.text = "\(number)"
        if let status = status {
            if status {
                contentView.backgroundColor = .systemGreen
                label.textColor = .white
                contentView.layer.borderColor = UIColor.systemGreen.cgColor
            } else {
                contentView.backgroundColor = .systemRed
                label.textColor = .white
                contentView.layer.borderColor = UIColor.systemRed.cgColor
            }
        } else {
            contentView.backgroundColor = .white
            label.textColor = .black
            contentView.layer.borderColor = UIColor.systemGray4.cgColor
        }
        // 当前题外圈加粗
        contentView.layer.borderWidth = isCurrent ? 3 : 2
    }
}

