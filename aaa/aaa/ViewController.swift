//
//  ViewController.swift
//  aaa
//
//  Created by apple on 2025/8/30.
//

import UIKit

class ViewController: UIViewController {

    // 题库：将从 Bundle 中的 questions.json 加载
    var questions: [[String: Any]] = []

    var currentQuestionIndex = 0

    var questionLabel: UILabel!
    var optionAButton: UIButton!
    var optionBButton: UIButton!
    var optionCButton: UIButton!
    var optionDButton: UIButton!
    var nextButton: UIButton!
    var prevButton: UIButton!
    var bottomStack: UIStackView!
    var answerStatuses: [Int: Bool] = [:] // 题目作答状态：true 正确 / false 错误


    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadQuestionsFromJSON()
        if questions.isEmpty {
            // 回退到两道示例题，避免界面空白
            questions = [
                ["question": "1840年，英国发动了侵略中国的鸦片战争，中国历史的发展从此发生重大转折。鸦片战争之所以被称为中国近代史的起点，是因为",
                 "options": ["A.清王朝内部满汉民族矛盾急剧恶化", "B.民族意识已经普遍觉醒", "C.农民起义规模空前", "D.中国的封建社会逐步变成了半殖民地半封建社会"],
                 "correctAnswer": "D"],
                ["question": "中国近代社会的两对主要矛盾是互相交织在一起的，而帝国主义和中华民族的矛盾，是最主要的矛盾。但是，在某个阶段、某个时期，阶级矛盾也会上升为主要矛盾，民族矛盾退居次要地位。以下根源于阶级矛盾上升为主要矛盾的事件是",
                 "options": ["A.义和团运动", "B.甲午战争", "C.鸦片战争", "D.辛亥革命"],
                 "correctAnswer": "D"]
            ]
        }
        loadQuestion()
    }

    func setupUI() {
        view.backgroundColor = .white
        title = "答题应用"
        // 右上角小三角按钮
        let triangle = UIButton(type: .system)
        triangle.setTitle("▾", for: .normal) // 小三角
        triangle.titleLabel?.font = UIFont.boldSystemFont(ofSize: 20)
        triangle.addTarget(self, action: #selector(showQuestionList), for: .touchUpInside)
        navigationItem.rightBarButtonItem = UIBarButtonItem(customView: triangle)

        // 创建问题标签
        questionLabel = UILabel()
        questionLabel.numberOfLines = 0
        questionLabel.textAlignment = .left
        questionLabel.font = UIFont.systemFont(ofSize: 18)
        questionLabel.textColor = .black
        questionLabel.lineBreakMode = .byWordWrapping
        questionLabel.setContentCompressionResistancePriority(.required, for: .vertical)
        questionLabel.setContentHuggingPriority(.required, for: .vertical)
        questionLabel.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(questionLabel)

        // 创建选项按钮
        optionAButton = createOptionButton()
        optionBButton = createOptionButton()
        optionCButton = createOptionButton()
        optionDButton = createOptionButton()

        // 创建底部固定区域：四个选项 + 上一题/下一题
        bottomStack = UIStackView(arrangedSubviews: [optionAButton, optionBButton, optionCButton, optionDButton, UIView(), navButtonsView()])
        bottomStack.axis = .vertical
        bottomStack.spacing = 10
        bottomStack.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(bottomStack)

        // 设置约束
        NSLayoutConstraint.activate([
            // 顶部问题标签
            questionLabel.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 20),
            questionLabel.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            questionLabel.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),

            // 底部固定区域
            bottomStack.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            bottomStack.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            bottomStack.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -16),

            // 选项按钮固定高度
            optionAButton.heightAnchor.constraint(equalToConstant: 50),
            optionBButton.heightAnchor.constraint(equalToConstant: 50),
            optionCButton.heightAnchor.constraint(equalToConstant: 50),
            optionDButton.heightAnchor.constraint(equalToConstant: 50),

            // 底部区域与问题标签的垂直关系
            bottomStack.topAnchor.constraint(greaterThanOrEqualTo: questionLabel.bottomAnchor, constant: 20)
        ])
    }

    func createOptionButton() -> UIButton {
        let button = UIButton(type: .system)
        button.backgroundColor = .white
        button.setTitleColor(.black, for: .normal)
        button.layer.cornerRadius = 8
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.systemGray4.cgColor
        button.titleLabel?.numberOfLines = 0
        button.contentHorizontalAlignment = .left
        button.contentEdgeInsets = UIEdgeInsets(top: 10, left: 15, bottom: 10, right: 15)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.addTarget(self, action: #selector(optionSelected), for: .touchUpInside)
        return button
    }

    private func navButtonsView() -> UIView {
        // “上一题 / 下一题”并排显示
        let container = UIStackView()
        container.axis = .horizontal
        container.alignment = .fill
        container.distribution = .fillEqually
        container.spacing = 12
        container.translatesAutoresizingMaskIntoConstraints = false

        prevButton = UIButton(type: .system)
        prevButton.setTitle("上一题", for: .normal)
        prevButton.backgroundColor = .systemGray5
        prevButton.setTitleColor(.black, for: .normal)
        prevButton.layer.cornerRadius = 8
        prevButton.translatesAutoresizingMaskIntoConstraints = false
        prevButton.addTarget(self, action: #selector(prevQuestion), for: .touchUpInside)

        nextButton = UIButton(type: .system)
        nextButton.setTitle("下一题", for: .normal)
        nextButton.backgroundColor = .systemBlue
        nextButton.setTitleColor(.white, for: .normal)
        nextButton.layer.cornerRadius = 8
        nextButton.translatesAutoresizingMaskIntoConstraints = false
        nextButton.addTarget(self, action: #selector(nextQuestion), for: .touchUpInside)

        container.addArrangedSubview(prevButton)
        container.addArrangedSubview(nextButton)
        return container
    }

    func loadQuestionsFromJSON() {
        guard let url = Bundle.main.url(forResource: "questions", withExtension: "json") else {
            print("questions.json not found in bundle")
            return
        }
        do {
            let data = try Data(contentsOf: url)
            if let arr = try JSONSerialization.jsonObject(with: data) as? [[String: Any]] {
                self.questions = arr
                print("Loaded questions count: \(arr.count)")
            }
        } catch {
            print("Failed to load questions.json: \(error)")
        }
    }

    func loadQuestion() {
        let question = questions[currentQuestionIndex]

        // 设置问题文本（确保非空并可显示）
        if let text = question["question"] as? String {
            questionLabel.text = "第\(currentQuestionIndex + 1)题：" + text
        } else {
            questionLabel.text = "(题目数据缺失)"
        }

        // 设置选项
        if let options = question["options"] as? [String], options.count >= 4 {
            optionAButton.setTitle(options[0], for: .normal)
            optionBButton.setTitle(options[1], for: .normal)
            optionCButton.setTitle(options[2], for: .normal)
            optionDButton.setTitle(options[3], for: .normal)
        } else {
            optionAButton.setTitle("A.", for: .normal)
            optionBButton.setTitle("B.", for: .normal)
            optionCButton.setTitle("C.", for: .normal)
            optionDButton.setTitle("D.", for: .normal)
        }

        // 重置按钮颜色
        resetButtonColors()

        // 触发布局更新，确保多行文本正确计算高度
        view.layoutIfNeeded()
    }

    func resetButtonColors() {
        optionAButton.backgroundColor = .white
        optionBButton.backgroundColor = .white
        optionCButton.backgroundColor = .white
        optionDButton.backgroundColor = .white
    }

    @objc func optionSelected(_ sender: UIButton) {
        // 将按钮映射为 A/B/C/D
        let selectedLetter: String
        switch sender {
        case optionAButton: selectedLetter = "A"
        case optionBButton: selectedLetter = "B"
        case optionCButton: selectedLetter = "C"
        case optionDButton: selectedLetter = "D"
        default: selectedLetter = ""
        }
        let correctAnswer = (questions[currentQuestionIndex]["correctAnswer"] as? String ?? "").trimmingCharacters(in: .whitespacesAndNewlines).uppercased()

        // 判断是否正确
        let isCorrect = (selectedLetter == correctAnswer)
        answerStatuses[currentQuestionIndex] = isCorrect
        if isCorrect {
            sender.backgroundColor = .systemGreen  // 正确
        } else {
            sender.backgroundColor = .systemRed    // 错误
        }

        // 显示正确答案
        showCorrectAnswer()
    }

    func showCorrectAnswer() {
        let correctAnswer = questions[currentQuestionIndex]["correctAnswer"] as? String ?? ""
        var correctButton: UIButton?

        // 根据正确答案，找到对应的按钮并显示为绿色
        switch correctAnswer {
        case "A":
            correctButton = optionAButton
        case "B":
            correctButton = optionBButton
        case "C":
            correctButton = optionCButton
        case "D":
            correctButton = optionDButton
        default:
            break
        }

        correctButton?.backgroundColor = .green  // 高亮显示正确答案
    }

    @objc func nextQuestion(_ sender: UIButton) {
        guard !questions.isEmpty else { return }
        currentQuestionIndex = (currentQuestionIndex + 1) % questions.count
        loadQuestion()
    }

    @objc func prevQuestion(_ sender: UIButton) {
        guard !questions.isEmpty else { return }
        currentQuestionIndex = (currentQuestionIndex - 1 + questions.count) % questions.count
        loadQuestion()
    }

    @objc func showQuestionList() {
        let vc = QuestionListViewController(total: questions.count, statuses: answerStatuses, current: currentQuestionIndex)
        vc.onSelect = { [weak self] index in
            guard let self = self else { return }
            self.currentQuestionIndex = index
            self.loadQuestion()
        }
        present(vc, animated: true)
    }
}
